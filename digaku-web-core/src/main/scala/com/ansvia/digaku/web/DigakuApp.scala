/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web

import java.io.{BufferedReader, File, FileReader}

import com.ansvia.digaku.web.util.PathUtil

/**
 * Author: robin (<EMAIL>)
 */

object DigakuApp {

    val VERSION = "3.3.19" // auto generated, don't change this.

    /**
     * Dapatkan versi revisi GIT dari file yang di-generate oleh
     * Makefile via: make vesion
     */
    lazy val GIT_REV = {
        _parsed._2
    }

    lazy val GIT_BRANCH = {
        _parsed._1
    }

    /**
     * Current digaku-web dir.
     */
    private lazy val currentDir = {
        new File(new File(new File(new File(getClass.getProtectionDomain.getCodeSource.getLocation.getPath)
            .getParent).getParent).getParent)
    }

    private lazy val _parsed = {
        var gitRev = new File(PathUtil.rootAppDir + "/GIT_REV")

        if (!gitRev.exists()) // try in /etc/
            gitRev = new File(PathUtil.rootAppDir + "/etc/GIT_REV")

        if (gitRev.exists()) {
            val br = new BufferedReader(new FileReader(gitRev))
            val s = br.readLine().trim.split("\\:")
            (s(0), s(1))
        }else
            ("???", "???")
    }


}

