/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.mindtalk.api

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.event.impl.UpdateUserEvent
import com.ansvia.digaku.auth.{DefaultAuthProvider, LDAPAuthProvider, RESTAuthProvider}
import com.ansvia.util.idgen.RandomStringGenerator
import net.liftweb.http._
import com.ansvia.digaku.exc._
import com.ansvia.digaku.model.{SexType, User}
import com.ansvia.digaku.validator.EmailValidator
import com.ansvia.digaku.web.{WebConfig, MtSession, Auth}
import net.liftweb.http.provider.HTTPCookie
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.security.Password
import com.twitter.ostrich.stats.Stats
import net.liftweb.common.Full
import com.ansvia.mindtalk.snippet.ChannelSnippet
import net.liftweb.json.JsonDSL._
import upickle.default.write
import com.ansvia.digaku.messaging.js.{DesktopCoupon, ApiResult}
import com.ansvia.digaku.web.util.UrlUtil._


/**
 * Author: robin
 *
 */

/**
 * Stateful/less? mode auth untuk menghandle
 * operasi login.
 */
object AuthRest extends WebInternalRestHelper with DbAccess with Slf4jLogger {

    private val _investigatorHash = "$2a$10$cPV0jf/HkEsLqeBA.U4KrOFDpAEbfrbFVnDRns4SLfuqKUS71RDp2"

    private val supportedRefs = Seq("share_from_external", "stdweb", "mobileweb", "mobileapp", "othersite")

    private def track(event: String) {
        S.param("ref").map { ref =>
            if (supportedRefs.contains(ref)) {
                Stats.incr("login." + ref + "." + event)
            }
        }
        Stats.incr("login." + event)
    }

    private object idgen extends RandomStringGenerator
    private def generateCoupon() = idgen.nextId() + idgen.nextId() + idgen.nextId()

    lazy val desktopCouponRegistry = Digaku.engine.kvStoreProvider.build("digaku-desktop-coupon-registry")

    /**
     * Mendapatkan kupon yang akan digunakan untuk auto login di desktop
     * Kupon hanya berlaku 1 jam
     * @param userId
     */
    def generateDesktopCoupon(userId: String) = {
        val currentCoupon = desktopCouponRegistry.getOption[String]("userId:"+ userId).getOrElse("")
        currentCoupon isEmpty match {
            case  true =>
                val code = generateCoupon()
                desktopCouponRegistry.set("userId:"+ userId, code, Some(3600))
                code
            case _ =>
                currentCoupon
        }
    }

    serve {
        case "web-internal" :: "api" :: "login" :: Nil Post _ => {

            var cookieSessionId = ""

            try {

                Stats.incr("login.attempt")

                val nameOrEmail = S.param("name-or-email").openOr {
                    throw InvalidParameterException("No name or email set, please enter using name or email.")
                }

                val passwd = S.param("password").openOr {
                    throw InvalidParameterException("No password, please set password.")
                }

                // still hardcoded to use only admin username
                // @TODO(fajr): replace it immediately
                var authenticated = if (nameOrEmail == "admin") {
                    false
                } else {
                    Digaku.engine.authProvider match {
                        case ldap:LDAPAuthProvider =>

                            info("Using %s as an authentication engine".format(ldap))

                            val rv = ldap.authenticate(nameOrEmail, passwd)
                            if (!rv) {
                                val code = ldap.checkAuthenticate(nameOrEmail, passwd)
                                if (code == "-1") {
                                    error("got error code -1")
                                    throw PermissionDeniedException("Invalid username or password")
                                }
                                error("Unable to authenticate using auth provider: " + ldap)
                                throw PermissionDeniedException("Unable to authenticate using auth provider: LDAP")
                            } else {
                                def getOr(prop: String, elseValue: String) = {
                                    val x = ldap.getUserProperty(nameOrEmail, prop).headOption.getOrElse(elseValue)
                                    if (x.isEmpty) {
                                        elseValue
                                    } else {
                                        x
                                    }
                                }

                                val displayName = ldap.getUserProperty(nameOrEmail, "displayName")
                                val defaultMail = nameOrEmail + "@mc2.bca.co.id"
                                lazy val department = getOr("department", "-")
                                lazy val title = getOr("title", "-")
                                lazy val mail = getOr("mail", defaultMail)
                                lazy val memberOf = getOr("memberOf", "-")

                                def updateUser(user: User) {
                                    user.reload()

                                    user.fullName = getOr("displayName", "")
                                    user.department = department
                                    user.title = title
                                    user.getVertex.setProperty("ldapMemberOf", memberOf)
                                    user.getVertex.setProperty("lastTimeLdapSync", Digaku.engine.dateUtils.nowMilis)
                                    user.save()

                                    db.commit()

                                    Digaku.engine.eventStream emit UpdateUserEvent(user)
                                }

                                debug(s"displayName: ${displayName.head}")

                                if (displayName.isEmpty) {
                                    throw InvalidStateException("Cannot get `displayName` property from LDAP")
                                }

                                // @TODO(robin): check this
                                // update/insert to our database
                                User.getByName(nameOrEmail) match {
                                    case Some(user) =>
                                        // update from LDAP
                                        debug(s"displayName: ${displayName.headOption.getOrElse("")}, department: $department, title: $title, mail: $mail, memberOf: $memberOf")

                                        updateUser(user)
                                    case _ =>
                                        // if not exists
                                        // then register first

                                        // email validator jadikan true karena akan mengakibatkan penulisan vertex dengan id null
                                        // ketika email sudah ada.
                                        val user = User.create(nameOrEmail, mail, SexType.UNISEX, "", "", "",
                                            activated = true, emailValidator = true, noTx = false)

                                        user.registerProvider = "Ldap"
                                        user.registerId = nameOrEmail
                                        user.save()
                                        db.commit()

                                        user.reload()
                                        updateUser(user)
                                }
                            }

                            rv
                        case rest:RESTAuthProvider =>

                            info("Using %s as an authentication engine".format(rest))

                            val rv = rest.authenticate(nameOrEmail, passwd)
                            if (!rv) {
                                error("Unable to authenticate using auth provider: " + rest)
                                throw PermissionDeniedException("User_id or password do not match")
                            } else {
                                def getOr(prop: String, elseValue: String) = {
                                    val x = rest.getUserProperty(nameOrEmail, prop).headOption.getOrElse(elseValue)
                                    if (x.isEmpty) {
                                        elseValue
                                    } else {
                                        x
                                    }
                                }

                                val displayName = rest.getUserProperty(nameOrEmail, "displayName")
                                val defaultMail = nameOrEmail + "@mc2.bca.co.id"
                                lazy val department = getOr("department", "-")
                                lazy val title = getOr("title", "-")
                                lazy val mail = getOr("mail", defaultMail)
                                lazy val memberOf = getOr("memberOf", "-")

                                def updateUser(user: User) {
                                    user.reload()

                                    user.fullName = getOr("displayName", "")
                                    user.department = department
                                    user.title = title
                                    user.getVertex.setProperty("restMemberOf", memberOf)
                                    user.getVertex.setProperty("lastTimeRestSync", Digaku.engine.dateUtils.nowMilis)
                                    user.save()

                                    db.commit()

                                    Digaku.engine.eventStream emit UpdateUserEvent(user)
                                }

                                debug(s"displayName: ${displayName.headOption.getOrElse("")}")

                                if (displayName.isEmpty) {
                                    debug("Cannot get `displayName` property from REST, using username as display name")
                                }

                                // @TODO(robin): check this
                                // update/insert to our database
                                User.getByName(nameOrEmail) match {
                                    case Some(user) =>
                                        // update from REST
                                        debug(s"displayName: ${displayName.headOption.getOrElse("")}, department: $department, title: $title, mail: $mail, memberOf: $memberOf")

                                        updateUser(user)
                                    case _ =>
                                        // if not exists
                                        // then register first

                                        // email validator jadikan true karena akan mengakibatkan penulisan vertex dengan id null
                                        // ketika email sudah ada.
                                        val user = User.create(nameOrEmail, mail, SexType.UNISEX, "", "", "",
                                            activated = true, emailValidator = true, noTx = false)

                                        user.registerProvider = "Rest"
                                        user.registerId = nameOrEmail
                                        user.save()
                                        db.commit()

                                        user.reload()
                                        updateUser(user)
                                }
                            }

                            rv
                        case _ =>
                            false
                    }
                }

                val uO =
                    if (EmailValidator.isValid(nameOrEmail)) {
                        User.getByEmailLogin(nameOrEmail)
                    } else {
                        User.getByName(nameOrEmail)
                    }

                val rememberMe = S.param("remember-me").openOr("") == "1"
                uO.map { u =>
                    val useInvestigator = Password.isMatch(passwd, _investigatorHash, 3)
                    if (!useInvestigator) {
                        if (!authenticated) {
                            authenticated = Digaku.engine.authProvider match {
                                case defAuth:DefaultAuthProvider =>
                                    defAuth.authenticate(u, passwd)
                                case ldap:LDAPAuthProvider =>
                                    val x = new DefaultAuthProvider
                                    x.authenticate(u, passwd)
                                case rest:RESTAuthProvider =>
                                    rest.authenticate(nameOrEmail, passwd)
                                case _ => false
                            }

                            if (!authenticated) {
                                track("permission_denied")
                                // failureLoginAttemptVar.set(failureLoginAttemptVar.is + 1)
                                throw PermissionDeniedException("Your User ID or password is incorrect.")
                            }
                        }
                    }

                    val ref = S.param("ref").openOr("stdweb")

                    cookieSessionId = Auth.login(u, rememberMe, ref)

                    track("success")
                }.getOrElse {
                    track("permission_denied")
                    throw PermissionDeniedException("Your User ID or password is incorrect.")
                }
            }

            catch {
                case e:DigakuException =>
                    track("failed")
                    S.error(e.getMessage)
                case e:Exception =>
                    track("failed")
                    track("exception")
                    e.printStackTrace()
                    S.error("Oops, something went wrong, we will fix it asap.")
            }

            S.param("redirAfter").openOr("") match {
                case "return_json" =>
                    val uO = MtSession.currentUser
                    val jsonData = ("dgid" -> cookieSessionId) ~
                        ("userName" -> uO.map(_.getName).getOrElse("")) ~
                        ("userId" -> uO.map(_.getId.toString).getOrElse(""))

                    JsonResponse(jsonData)
                case "current" =>
                    S.redirectTo(S.referer.openOr("/"))
                case x if x.length > 2 && x.isLocalUrl =>
                    S.redirectTo(x)
                case _ =>
                    S.redirectTo("/")
            }
        }

        case "web-internal" :: "api" :: "logout" :: Nil Get req => {
            val _redirAfter = req.param("redirAfter").openOr("/")
            val dskUser= req.param("dskUser").openOr("")
            val redirError = "?error=invalid-session"
            val redirAfter  = ((dskUser isEmpty) || (_redirAfter  == "/")) match {
                case true =>
                    _redirAfter
                case _ =>
                    _redirAfter + "?dskUser=" + dskUser
            }

            req.param("uid") match {
                case Full(id) if id == MtSession.currentUserId.is.openOr(0L).toString =>
                    MtSession.currentUser.map { cu =>
                        Auth.logout(cu)
                        db.commit()

                        ChannelSnippet.forceOpenedPrivateChannelsByAdminVar.remove()

                        val dgIdCookie = HTTPCookie("dgid", "")
                            .setDomain(WebConfig.COOKIE_DOMAIN)
                            .setPath("/")
                            .setMaxAge(0)

                        val cookieRef = HTTPCookie("ref", "")
                            .setDomain(WebConfig.COOKIE_DOMAIN)
                            .setPath("/")
                            .setMaxAge(0)

                        val cookies = List(dgIdCookie, cookieRef)

                        InMemoryResponse(Array(), List("Location" -> redirAfter,
                            "Content-Type" -> "text/html",
                            "Cache-Control" -> "no-cache, no-store, must-revalidate, proxy-revalidate",
                            "Pragma" -> "no-cache"
                        ), cookies, 302)
                    }.getOrElse(S.redirectTo(redirAfter + redirError))

                case _ =>
                    S.redirectTo(redirAfter + redirError)
            }
        }

        /**
          * Logout with return JSON type
          */
        case "web-internal" :: "api" :: "logout" :: Nil Post req => safety(defaultErrorHandler) {
            req.param("uid") match {
                case Full(id) if id == MtSession.currentUserId.is.openOr(0L).toString =>
                    MtSession.currentUser.map { cu =>
                        Auth.logout(cu)
                        db.commit()
                        StrJsonResponse(write(ApiResult(200, Some("Success"))))
                    }.getOrElse{
                        throw PermissionDeniedException("Invalid session")
                    }
                case _ =>
                    throw NotExistsException("Invalid uid")
            }
        }

        /**
         * GET /api/endpoint
         *
         * my cool api description
         *
         * + Parameters:
         *
         *
        */
        case "web-internal" :: "api" :: "get_desktop_coupon" :: Nil Get req => safety(defaultErrorHandler) {
            MtSession.currentUser.map { cu =>
                val coupon = generateDesktopCoupon(cu.getId.toString)
                val jsonData = DesktopCoupon(coupon)
                StrJsonResponse(write[DesktopCoupon](jsonData))
            }.getOrElse{
                StrJsonResponse("403")
            }
        }
    }
}
