/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.mindtalk.snippet

import java.io.{BufferedReader, File, FileReader}

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.persistence._
//import com.ansvia.digaku.plugin.DigakuPlugin.WidgetPosition
import com.ansvia.digaku.web.util.PathUtil
import com.ansvia.digaku.web.{MailSettings, MtSession, WebConfig}
import com.ansvia.liftweb.lib.UserAgent
import com.ansvia.mindtalk.Config
//import com.ansvia.mindtalk.lib.PluginManager
import net.liftweb.http.S
import net.liftweb.util.Helpers._
import net.liftweb.util.Props
import org.apache.cassandra.thrift.{Cassandra, TBinaryProtocol}
import org.apache.thrift.transport.{TFramedTransport, TSocket}

import scala.xml.NodeSeq

/**
 * Author: robin
 *
 */
object DigakuApp extends Slf4jLogger {

    val VERSION = "3.3.19" // auto generated, don't change this.

    private val GIT_REV = com.ansvia.digaku.web.DigakuApp.GIT_REV

    /**
     * Menampilkan informasi version dll di footer setiap halaman.
     */
    def footerInfo:NodeSeq = {
        <div style="color: #909090; font-size: smaller;">
            <span>Digaku Engine version {VERSION}</span><br />
            {
                if (Props.mode == Props.RunModes.Development || Props.mode == Props.RunModes.Test){
                    <xml:group>
                        {
                        if (GIT_REV != "unknown")
                            <span>GIT: <a href={"https://repository.mindtalk.com/digaku2/commit/" + GIT_REV}>{GIT_REV}</a></span><br />
                        else
                            <span>GIT: {GIT_REV}</span><br />
                        }
                        <!--<span>Developer: <a href="/u/robin">robin</a> &amp; <a href="/u/ubai">ubai</a></span><br />-->
                        <small>{S.request.openOrThrowException("should has user agent")
                            .userAgent.map( userAgent => <span>User Agent: {userAgent}</span>:NodeSeq ).openOr(NodeSeq.Empty)}</small>
                        <br />
                    </xml:group>
                } else
                    NodeSeq.Empty
            }

            {
                if(UserAgent.isMobile){
                    import com.ansvia.digaku.utils.UserSettings._
                    MtSession.currentUser map { cu =>
                        val layout = cu.settings.get("layout", "mobile")
                        if(layout == "mobile"){
                            <div><small><a href="/sys/layout?switch=desktop">Switch to Desktop Layout</a></small></div>
                        } else {
                            <div><small><a href="/sys/layout?switch=mobile">Switch to Mobile Layout</a></small></div>
                        }
                    } getOrElse NodeSeq.Empty
                } else
                    NodeSeq.Empty
            }

            {
//                // additional from plugin
//                PluginManager.activePlugins
//                    .filter(_.hasWidget(WidgetPosition.HomeFooter))
//                    .map(_.getWidget(WidgetPosition.HomeFooter, null))
//                    .reduceLeftOption(_ ++ _)
//                    .getOrElse(NodeSeq.Empty)
            }

        </div>

    }

    /**
     * Controller untuk menghandle layout switcher.
     */
    def layoutSwitch:NodeSeq = {
        import com.ansvia.digaku.utils.UserSettings._
        MtSession.currentUser.map { user =>
            S.param("switch") map {
                case layout @ ("desktop" | "mobile") =>
                    user.settings.set("layout", layout)
                    var referer = S.referer.getOrElse("/")
                    referer =
                        if (!referer.toLowerCase.contains("layout"))
                            referer
                        else
                            "/"

                    <xml:group>
                        <h5>Layout switched to
                            {layout}
                            , please wait,
                            this will redirect to previous page, if not please
                            <a href={referer}>click here</a>
                            .</h5>
                        <script type="text/javascript">
                            {"""setTimeout(function(){
                               |   window.location = '%s';
                               |}, 2000);""".stripMargin.format(referer)}
                        </script>
                    </xml:group>
                case x =>
                    <h5>Unknown layout
                        {x}
                        . Support only desktop / mobile.</h5>
            } openOr NodeSeq.Empty
        } getOrElse NodeSeq.Empty
    }

    /**
     * Untuk menampilkan hasil operasi.
     */
    def operationResult:NodeSeq = {
        MtSession.operationResult map { result =>
            if(result.trim.length > 0){
                <h5>{result}</h5>
            } else
                NodeSeq.Empty
        } openOr NodeSeq.Empty
    }

    def allowFooter(in:NodeSeq):NodeSeq = {
        val path = S.uri
        if (!(path == "/index" || path == "/") || (UserAgent.isMobile && !UserAgent.isIpad))
            in
        else
            NodeSeq.Empty
    }


    def environments(in:NodeSeq):NodeSeq = {
        val (hostName, port) = {
            val s = Digaku.config.mainDatabase.hostName.trim.split(":")
            (s(0), s(1).toInt)
        }
        val tr = new TFramedTransport(new TSocket(hostName, port))
        val proto = new TBinaryProtocol(tr)

        val client = new Cassandra.Client(proto)

        tr.open()

        val cassandraVersion = client.describe_version()
        val titanVersion = Digaku.engine.database.getVersion

        val rv = {
            bind("in", in,
                "app-id" -> WebConfig.NODE_ID,
                "machine-id" -> Config.MACHINE_ID,
                "digaku" -> DigakuApp.VERSION,
                "digaku-git" -> GIT_REV,
                "cassandra-thrift" -> cassandraVersion,
                "titan" -> titanVersion,
                "cassandra-keyspace" -> Digaku.config.mainDatabase.keyspaceName,
                "cassandra-repl-strategy" -> Digaku.config.mainDatabase.replStrategy,
                "cassandra-repl-strategy-opts" -> Digaku.config.mainDatabase.replStrategyOpts,
                "smtp" -> "%s:%d".format(MailSettings.SMTP_HOST, MailSettings.SMTP_PORT),
                "es-version" -> (try {
                        Digaku.engine.searchEngine.getVersion
                    }catch{
                        case e:org.elasticsearch.client.transport.NoNodeAvailableException =>
                            "unknown"
                    })
            )
        }

        tr.close()

        rv
    }

    def systems:NodeSeq = {

        <div>
            <div>
                <legend>AWS</legend>
                <table class="table table-striped">
                    <tr><td style="width: 250px;">S3 bucket</td><td>{Config.AWS_S3_BUCKET}</td></tr>
                    <tr><td style="width: 250px;">access key</td><td>{Config.AWS_ACCESS_KEY}</td></tr>
                    <tr><td style="width: 250px;">secret key</td><td>{Config.AWS_SECRET_KEY}</td></tr>
                    <tr><td style="width: 250px;">CDN domain</td><td>{Config.conf("aws.cloudfront-domain", "-")}</td></tr>
                </table>
            </div>
            <div>
                <legend>Etc</legend>
                <table class="table table-striped">
                    <tr><td style="width: 250px;">CLIENT_ID</td><td>{Config.CLIENT_ID}</td></tr>
                    <tr><td style="width: 250px;">DOMAIN_NAME</td><td>{WebConfig.DOMAIN_NAME}</td></tr>
                    <tr><td style="width: 250px;">BASE_URL</td><td>{WebConfig.BASE_URL}</td></tr>
                    <tr><td style="width: 250px;">Group Url Prefix</td><td>{WebConfig.CHANNEL_URL_PREFIX}</td></tr>
                    <tr><td style="width: 250px;">User Url Prefix</td><td>{WebConfig.USER_URL_PREFIX}</td></tr>
                    <tr><td style="width: 250px;">Maintenance mode</td><td>{Digaku.engine.systemConfig.get("system.maintenance", defVal = false)}</td></tr>

                    {
                    // IdFactory specific information

                    <tr><td style="width: 250px;">Id Factory</td><td>{Digaku.engine.idFactory.getClass.getCanonicalName}</td></tr> ++
                    (Digaku.engine.idFactory match {
//                        case idFactory:CassandraBackedIdFactory =>
//                            val idBatch = idFactory.csid.asInstanceOf[CassandraId].getCurrentBatch
//                            <tr><td style="width: 250px;">Current ID batch</td><td>{idBatch}</td></tr>
                        case idFactory:SnowFlakeIdFactory =>
                            <tr><td style="width: 250px;">Last generated ID</td><td>{idFactory.getLastGeneratedId}</td></tr>
                        case _ =>
                            NodeSeq.Empty
                    })

                    }


                </table>
            </div>
        </div>
    }

    def paths = {
        <div>
            <table class="table table-striped">
                <tr><td style="width: 250px;">CASSANDRA_HOST</td><td>{Digaku.config.mainDatabase.hostName}</td></tr>
                <tr><td>TMP_PATH</td><td>{Config.TMP_PATH}</td></tr>
                <!-- <tr><td>FAUNUS_PATH</td><td>{Config.FAUNUS_PATH}</td></tr> -->
            </table>
        </div>
    }


    def title:NodeSeq = {
        <title>{WebConfig.BRAND_NAME}</title>
    }


}
