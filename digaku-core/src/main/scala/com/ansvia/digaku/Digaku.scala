/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku

import akka.actor.ActorSystem
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.analytic.AnalyticComponent
import com.ansvia.digaku.auth.{DefaultAuthProvider, AuthProvider}
import com.ansvia.digaku.cluster.{CacheInvalidatorInternodeMessageHandler, InternodeCommunicatorComponent}
import com.ansvia.digaku.config.{Config, ConfigComponent}
import com.ansvia.digaku.dao.DaoListIndexFactory
import com.ansvia.digaku.database.TitanDatabaseImpl
import com.ansvia.digaku.event.impl.StartupEvent
import com.ansvia.digaku.event.{EventStreamListener, EventStreamComponent, EventStream}
import com.ansvia.digaku.notifications.{BlockingNotificationEventStreamListener, NotificationEventStreamListener, _}
import com.ansvia.digaku.persistence._
import com.ansvia.digaku.se.{BlockingSEEventStreamListener, SEEventStreamListener, SearchEngineComponent}
import com.ansvia.digaku.stats.BlockingAnalEventStreamListener
import com.ansvia.digaku.stream._
import com.ansvia.digaku.utils.{ContentProcessorComponent, DateUtils}
import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.serializers.{StringSerializer, LongSerializer, ByteSerializer}


trait TimeProviderComponent {
    def dateUtils: DateUtils
}


trait KVStoreProviderComponent {
    private lazy val kvStoreFactory = new CassandraBackedKVStoreFactory(Global.STANDARD_CF_NAME,
        Digaku.config.mainDatabase.keyspaceName, Digaku.config.mainDatabase.clusterName,
        Digaku.config.mainDatabase.hostName,
        Digaku.config.mainDatabase.replStrategy, Digaku.config.mainDatabase.replStrategyOpts
    )

    def kvStoreProvider: CassandraBackedKVStoreFactory = kvStoreFactory
}


trait SeqStoreProviderComponent {
    private val CF = new ColumnFamily[String, java.lang.Long](
        Global.STANDARD_CF_NAME,
        StringSerializer.get(),
        LongSerializer.get(),
        ByteSerializer.get())

    private lazy val seqStore = new CassandraBackedSeqStore(CF,
        Digaku.config.mainDatabase.keyspaceName,
        Digaku.config.mainDatabase.clusterName,
        Digaku.config.mainDatabase.hostName,
        Digaku.config.mainDatabase.replStrategy,
        Digaku.config.mainDatabase.replStrategyOpts)
        .setComparatorType("LongType(reversed=true)")

    def seqStoreProvider = seqStore
}


trait AuthProviderComponent {
    val authProvider: com.ansvia.digaku.auth.AuthProvider[_, Boolean]
}


/**
 * Author: robin
 *
 */
trait Engine extends ConfigComponent
        with TitanDatabaseImpl
        with IdFactoryComponent
        with CounterProviderComponent
        with AnalyticComponent
        with ContentProcessorComponent
        with SearchEngineComponent
        with StreamBuilderComponent
        with EventStreamComponent
        with InternodeCommunicatorComponent
        with TimeProviderComponent
        with KVStoreProviderComponent
        with SeqStoreProviderComponent
        with AuthProviderComponent
        with Slf4jLogger {

    val version = "3.3.19" // auto generated, don't change this.

    var initialized = false

    val commonCassandraCf: CassandraDriver[String, String]

    lazy val actorSystem = ActorSystem("digaku-internal")

    lazy val eventStream = new EventStream

    override val streamBuilder: StreamBuilderEngine = StreamBuilder

    override lazy val authProvider: AuthProvider[_, Boolean] = new DefaultAuthProvider()

    override def dateUtils: DateUtils = new DateUtils {}

    def init() {
        /**
         * Init configuration.
         */
        Config.init()

        synchronized {
            if (!initialized){
                info("Initializing Digaku Engine...")

                initializeInternal()
            }
        }
    }

    private var notificationListener:EventStreamListener = _

    def getNotificationListener = notificationListener

    private def initializeInternal() {

        database.index()

        if (config.eventStreamSupport) {

            eventStream.setBlocking(config.useBlockingEventStream)

            // event stream listeners setup
            if (!config.useBlockingEventStreamListener) {
                notificationListener = NotificationEventStreamListener()
                eventStream.addListeners(notificationListener)
            } else {
                notificationListener = BlockingNotificationEventStreamListener()
                eventStream.addListeners(notificationListener)
            }

            StreamBuilder.duplicateDetector = new CassandraBackedDuplicateDetector(
                config.mainDatabase.clusterName, config.mainDatabase.keyspaceName,
                config.mainDatabase.hostName
            )

            /**
             * Apabila menggunakan external stream builder
             * maka perlu mengimplementasi listener sendiri untuk
             * melakukan stream processing, sehingga StreamBuilderDispatcher di sini tidak perlu didaftarkan
             * sebagai listener.
             */
            if (!config.usingExternalStreamBuilder){
                /**
                 * Gunakan 20 workers untuk mengelola stream.
                 */
                eventStream.addListeners(new StreamBuilder(
                    (for (i <- 0 to 30) yield new StreamBuilderDispatcher(i)): _*
                ))
            }

            if (config.useBlockingSEEStreamListener)
                eventStream.addListeners(BlockingSEEventStreamListener)
            else
                eventStream.addListeners(SEEventStreamListener)

            if (config.withAnalEventStreamListener) {
                if (config.useBlockingAnalEventStreamListener)
                    eventStream.addListeners(BlockingAnalEventStreamListener)
                else
                    eventStream.addListeners(analytic.listener)
            }

            NotificationSender.setBlocking(config.useBlockingNotifierSender)

            if (!config.useBlockingNotifierSender) {
                NotificationSender.start()
            }

            eventStream.emit(StartupEvent())
        }

        if (config.supportNsq) {
            interNodeComm.registerHandlers(new CacheInvalidatorInternodeMessageHandler(config.nodeId))
        }

        /**
         * Setup RootVertex List Storer engine
         */
        val dbConf = this.config.dbConf

        DaoListIndexFactory.clusterName = dbConf.getString("storage.cluster-name")
        DaoListIndexFactory.keyspaceName = dbConf.getString("storage.cassandra.keyspace")
        DaoListIndexFactory.seeds = dbConf.getString("storage.hostname")
        DaoListIndexFactory.replicationStrategy = dbConf.getString("storage.cassandra.replication-strategy-class")

        val strategyOpts = dbConf.getStringArray("storage.cassandra.replication-strategy-options")
        val strategyOptsStr = strategyOpts.toList.grouped(2).map(a => s"${a(0)}:${a(1)}").mkString(",")

        DaoListIndexFactory.replicationStrategyOpts = strategyOptsStr

        assert(DaoListIndexFactory.clusterName != null, "config `storage.cluster-name` not set, please set first")
        assert(DaoListIndexFactory.keyspaceName != null, "config `storage.cassandra.keyspace` not set, please set first")

        info("Initialization completed.")
        initialized = true
    }

    def shutdown() {
        synchronized {
            if (initialized) {
                info("stopping notification sender...")
                NotificationSender.setStop(state = true)
                NotificationSender.join()
                info("notification sender stopped.")

                /**
                 * Kill all async listeners worker actors.
                 */
                eventStream.close()

                info("closing database...")
                database.close()

                info("closing search engine...")
                searchEngine.close()

                Config.reset()

                actorSystem.shutdown()

                info("bye.")

                initialized = false
            }
        }
    }

    def print(){}
}


object Digaku extends ConfigComponent {
    var engine:Engine = _

    lazy val config = engine.config
    lazy val systemConfig = engine.systemConfig

    def init() {
        require(engine != null, "engine not set yet, please set Digaku.engine = [YOUR-ENGINE]")
        engine.init()
    }

    def shutdown() {
        engine.shutdown()
    }
}
