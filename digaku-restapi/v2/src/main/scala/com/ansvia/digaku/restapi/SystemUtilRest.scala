/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.restapi

import java.io.{BufferedReader, File, FileReader}

import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, NotExistsException}
import com.ansvia.digaku.model.BaseModel
import com.ansvia.digaku.{Digaku, model => dgmodel}
import com.ansvia.graph.BlueprintsWrapper._
import net.liftweb.http._
import net.liftweb.http.rest._
import net.liftweb.json.JsonDSL._
import net.liftweb.json._
import net.liftweb.util.BasicTypesHelpers.AsLong

/** apidoc **
 * GROUP: Me
 */
object SystemUtilRest extends RestHelper with DigakuRestHelper {


    val WEB_VERSION = "3.3.19" // auto generated, don't change this.
    val RESTAPI_VERSION = "3.1.18" // auto generated, don't change this.

    /**
     * Current dir.
     */
    private lazy val currentDir = {
        new File(new File(new File((new File(getClass.getProtectionDomain.getCodeSource.getLocation.getPath))
            .getParent).getParent).getParent)
    }

    private lazy val reg = //WebConfig.getConfiguredKVStore("Standard1","apps_config")
        Digaku.engine.kvStoreProvider.build("apps_config")

    /**
     * Dapatkan versi revisi GIT dari file yang di-generate oleh
     * Makefile via: make vesion
     */
    private lazy val GIT_REV = {
        val gitRev = new File(currentDir.getAbsolutePath + "/GIT_REV")
        if (gitRev.exists())
            new BufferedReader(new FileReader(gitRev)).readLine().trim
        else
            "unknown"
    }

    serve {

        case "system" :: "info" :: Nil Get req =>


            var rv = ("core" -> ("version" -> WEB_VERSION)) ~ // web version === core version
                ("digaku_restapi" -> ("version" -> RESTAPI_VERSION)) ~
                ("server_time" -> Digaku.engine.dateUtils.nowMilis):JObject

            authorizedOnly(req) {
                case AuthorizedOAuthSession(user, app, _, _) => {
                    lazy val configurator = Digaku.systemConfig
                    if (app.internal) {
                        rv = rv ~ ("git_rev" -> GIT_REV) ~
                            ("search" ->
                                ("engine" -> configurator.get("search.engine", "abc")) ~
                                    ("index-dir" -> configurator.get("search.engine.index-dir", "yyy")) ~
                                    ( "host" -> configurator.get("search.engine.host", "xxx"))
                                ):JObject
                    }

                    ("result" -> rv):JValue
                }
        }

        /**
         * Unpublished endpoint
         * digunakan untuk melakukan peng-index-an manual
         *
         */
        case "system" :: "search" :: "manual_index" :: idToIndex :: Nil Post req => authorizedOnly(req) {
            case AuthorizedOAuthSession(user, app, _, _) => {

                idToIndex match {
                    case AsLong(objectId) =>
                        try {
                            val v = db.getVertex(objectId)

                            var targetObj:BaseModel[IDType] = null

                            v.toCC[BaseModel[IDType]].map { obj =>

                                import com.ansvia.digaku.model._

                                obj match {
                                    case a:Article =>
                                    case a:Picture =>
                                    case a:User =>
                                    case a:Forum =>
//                                    case a:Advertisement =>
//                                    case a:PrivateMessage =>
                                    case a:App =>
//                                    case a:SponsorPost =>
                                    case x =>
                                        throw InvalidParameterException("object not supported: " + x + ", " + v)
                                }

                                Digaku.engine.searchEngine.index(obj)
                                targetObj = obj
                            }

                            if (targetObj != null){

                                val data = ("msg" -> "%s indexed successfully".format(targetObj))

                                success(data)
                            }else{

                                throw NotExistsException("Index failed obj=null")
                            }
                        }catch{
                            case e:Exception =>
                                throw new DigakuException(e.getMessage)
                        }
                    case name:String =>
                        throw NotExistsException("No id with name " + name)

                }


            }
        }

        /**
         * Digunakan untuk mendapatkan value dari apps config berformat key-value pair yang
         * disediakan khusus untuk apps developer.
         * Berkaitan dengan code di com.ansvia.mindtalk.snippet.AdminAppsReleaseManSnippet
         */
        case "system" :: "apps_config" :: key :: Nil Get req => {

            val defaultValue = S.param("default").openOr("")

            val value = reg.get(key, defaultValue)

            val rv = try {
                JsonParser.parse(value)
            } catch {
                case e:JsonParser.ParseException => JString(value)
            }

            success(rv)

        }


    }

}







